import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GeminiBalanceClient, getBalanceModelNameForDatabase } from '../_shared/gemini-balance-client.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ProcessorResponse {
  success: boolean;
  processed_tasks?: number;
  errors?: string[];
  message?: string;
}

// Initialize Gemini client with longer timeout for transcript generation
const geminiClient = new GeminiBalanceClient(undefined, 180000); // 180s timeout (3 minutes) for large content

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client with service role key for cron job access
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('🎙️ Starting podcast transcript processor...');

    // Check if any task is currently being processed (additional safety check)
    const { data: processingTasks } = await supabaseClient
      .from('podcast_tasks')
      .select('id')
      .eq('status', 'transcript_generating');

    if (processingTasks && processingTasks.length > 0) {
      console.log('⏳ Another transcript is currently being generated, skipping...');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'Another transcript generation in progress'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Find pending tasks that need transcript generation
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('podcast_tasks')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true })
      .limit(1); // Process one task at a time to avoid timeout

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('📭 No pending tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'No pending tasks found'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const task = pendingTasks[0];
    console.log(`📝 Processing task: ${task.id} for topic: ${task.metadata?.topic_name}`);

    // Update task status to transcript_generating
    await supabaseClient
      .from('podcast_tasks')
      .update({
        status: 'transcript_generating',
        progress_percentage: 10,
        updated_at: new Date().toISOString()
      })
      .eq('id', task.id);

    // Add heartbeat mechanism for long-running tasks
    const heartbeatInterval = setInterval(async () => {
      try {
        await supabaseClient
          .from('podcast_tasks')
          .update({
            updated_at: new Date().toISOString(),
            metadata: {
              ...task.metadata,
              last_heartbeat: new Date().toISOString()
            }
          })
          .eq('id', task.id);
        console.log(`💓 Heartbeat sent for task ${task.id}`);
      } catch (heartbeatError) {
        console.warn(`⚠️ Heartbeat failed for task ${task.id}:`, heartbeatError);
      }
    }, 30000); // Send heartbeat every 30 seconds

    try {
      // Generate transcript
      const transcript = await generatePodcastTranscript(task);

      // Estimate segments count for TTS processing
      const estimatedSegments = estimateSegmentsCount(transcript);

      // Update task with transcript and move to next stage
      const { error: updateError } = await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'transcript_ready',
          transcript: transcript,
          total_segments: estimatedSegments,
          progress_percentage: 25,
          metadata: {
            ...task.metadata,
            transcript_generated_at: new Date().toISOString(),
            transcript_length: transcript.length,
            estimated_segments: estimatedSegments
          }
        })
        .eq('id', task.id);

      if (updateError) {
        throw new Error(`Failed to update task: ${updateError.message}`);
      }

      console.log(`✅ Successfully generated transcript for task ${task.id}`);
      console.log(`📊 Transcript length: ${transcript.length} characters, estimated segments: ${estimatedSegments}`);

    } catch (transcriptError) {
      console.error(`❌ Failed to generate transcript for task ${task.id}:`, transcriptError);

      // Determine error type for better error handling
      let errorMessage = `Transcript generation failed: ${transcriptError.message}`;
      let shouldRetry = true;

      // Check if it's a timeout error
      if (transcriptError.message.includes('timeout') || transcriptError.message.includes('AbortError')) {
        errorMessage = `Transcript generation timed out after 3 minutes. Content may be too large.`;
        console.error(`⏰ Timeout detected for task ${task.id}. Consider splitting large content.`);
      }

      // Check if it's an API error that shouldn't be retried
      if (transcriptError.message.includes('API error') && transcriptError.message.includes('400')) {
        shouldRetry = false;
        errorMessage = `API request invalid: ${transcriptError.message}`;
      }

      try {
        // Update task with error - ensure this always succeeds
        const { error: updateError } = await supabaseClient
          .from('podcast_tasks')
          .update({
            status: shouldRetry ? 'failed' : 'failed_permanent',
            error_message: errorMessage,
            retry_count: (task.retry_count || 0) + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', task.id);

        if (updateError) {
          console.error(`❌ Failed to update task status after error:`, updateError);
          // Force update with minimal data to ensure status is not stuck
          await supabaseClient
            .from('podcast_tasks')
            .update({
              status: 'failed',
              error_message: 'Error updating task status',
              updated_at: new Date().toISOString()
            })
            .eq('id', task.id);
        } else {
          console.log(`📝 Task ${task.id} status updated to failed with error: ${errorMessage}`);
        }
      } catch (updateError) {
        console.error(`❌ Critical error: Could not update task status for ${task.id}:`, updateError);
      }

      throw transcriptError;
    } finally {
      // Always clear the heartbeat interval
      clearInterval(heartbeatInterval);
      console.log(`🛑 Heartbeat cleared for task ${task.id}`);
    }

    return new Response(
      JSON.stringify({
        success: true,
        processed_tasks: 1,
        message: `Successfully processed task ${task.id}`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('❌ Transcript processor error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        errors: [error.message]
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

async function generatePodcastTranscript(task: any): Promise<string> {
  const summariesData = task.metadata?.summaries_data || [];
  const taskLanguage = task.language || 'ZH'; // Default to Chinese if not specified

  if (summariesData.length === 0) {
    throw new Error('No summaries data found in task metadata');
  }

  // Prepare content for transcript generation based on language
  let summariesContent: string;
  let prompt: string;

  if (taskLanguage === 'EN') {
    // English content formatting
    summariesContent = summariesData.map((summary: any, index: number) => {
      return `${index + 1}. [${summary.source_name} - ${summary.platform}] ${summary.title}\n${summary.content}\n`;
    }).join('\n');

    // English prompt
    prompt = `You are a professional podcast producer. Based on the following daily summaries related to ${task.metadata?.topic_name}, generate a 40-minute English podcast script with two hosts for FeedMe.Today daily podcast.

Character Setup:
- Host (Joy): Professional, good at guiding conversations and asking questions, friendly and natural speaking style
- Guest (Sam): Technical expert, provides in-depth analysis and insights, professional but accessible language

Requirements:
1. GENERATE CONTENT ENTIRELY IN ENGLISH - this is an English podcast for English-speaking audience
2. Start with a brief mention that this is FeedMe.Today's daily podcast, then dive directly into the content discussion
3. Natural conversation flow, avoid rigid Q&A format, create authentic dialogue
4. Group related topics for discussion, deep dive into each topic thoroughly
5. Total duration MUST be 40 minutes (approximately 10000-20000 words, which is about 25000-40000 characters in English)
6. Strictly use "Joy:" and "Sam:" to mark speakers
7. Interactive dialogue with appropriate responses, supplements, and questions
8. Language should fit English podcast expression habits, natural and fluent
9. Ensure each speaking segment is moderate length for TTS processing
10. When discussing content from non-English sources, provide context and explanation in English
11. DO NOT use any Chinese characters or phrases - keep everything in English
12. Make sure all summary content are included in the podcast
13. NO opening music, closing remarks, or narrator content - only direct conversation between the two hosts
14. Focus purely on content discussion without unnecessary introductions or transitions

Today's Summary Content (${summariesData.length} items):
${summariesContent}

Please generate the complete English podcast dialogue script:`;
  } else {
    // Chinese content formatting
    summariesContent = summariesData.map((summary: any, index: number) => {
      return `${index + 1}. 【${summary.source_name} - ${summary.platform}】${summary.title}\n${summary.content}\n`;
    }).join('\n');

    // Chinese prompt
    prompt = `你是一个专业的播客制作人。请基于以下${task.metadata?.topic_name}相关的每日摘要内容，生成一个60分钟的FeedMe.Today每日播客双人对话脚本。

角色设定：
- 主持人（Alex）：专业、引导性强，负责串联话题和提问，语言风格亲切自然
- 嘉宾（Jessie）：技术专家，提供深度分析和见解，语言风格专业但易懂

要求：
1. 开头简单提及这是FeedMe.Today的每日播客，然后直接进入内容讨论
2. 自然的对话流程，避免生硬问答，要有真实的对话感
3. 按相关话题分组讨论，每个话题深入探讨
4. 总时长必须达到60分钟（约15000-30000字）
5. 严格使用"Alex："和"Jessie："标记发言人
6. 对话要有互动感，包含适当的回应、补充、提问
7. 语言要符合中文播客的表达习惯，自然流畅
8. 确保每个发言段落长度适中，便于后续TTS处理
9. 如果包含英文内容摘要，请自然地融入中文讨论中
10. 要保证摘要里面的所有内容都在播客里面说出来
11. 不要加入任何旁白、开场音乐、结尾总结等内容，只要两个主持人的纯对话
12. 专注于内容讨论，不需要不必要的介绍或过渡

今日摘要内容（共${summariesData.length}条）：
${summariesContent}

请生成完整的播客对话脚本：`;
  }

  console.log(`🤖 Generating transcript with Gemini for ${summariesData.length} summaries...`);
  console.log(`📊 Content length: ${summariesContent.length} characters`);

  try {
    const startTime = Date.now();
    const content = await geminiClient.simpleChat(prompt, {
      maxTokens: 60000,
      temperature: 0.7
    });
    const endTime = Date.now();

    console.log(`⏱️ Transcript generation took ${(endTime - startTime) / 1000}s`);

    if (!content || content.trim().length === 0) {
      throw new Error('No content generated from Gemini - empty response');
    }

    console.log(`📝 Generated transcript length: ${content.length} characters`);
    return content;

  } catch (error) {
    console.error('❌ Failed to generate transcript:', error);

    // Provide more specific error information
    if (error.message.includes('timeout')) {
      throw new Error(`Gemini API timeout: Content too large (${summariesContent.length} chars, ${summariesData.length} summaries)`);
    } else if (error.message.includes('AbortError')) {
      throw new Error(`Request aborted: Likely due to timeout or network issues`);
    } else if (error.message.includes('API error')) {
      throw new Error(`Gemini API error: ${error.message}`);
    } else {
      throw new Error(`Transcript generation failed: ${error.message}`);
    }
  }
}

function estimateSegmentsCount(transcript: string): number {
  // Split by speaker changes - support both Chinese and English speakers (with both colon types)
  const speakerChanges = transcript.split(/(?=(?:Alex[：:]|Jessie[：:]|Joy:|Sam:))/g).filter(segment => segment.trim().length > 0);

  // Each segment should be roughly 2 minutes of speech (about 300-400 characters)
  const targetSegmentLength = 350;
  let totalSegments = 0;

  for (const segment of speakerChanges) {
    const segmentLength = segment.length;
    const segmentsNeeded = Math.ceil(segmentLength / targetSegmentLength);
    totalSegments += segmentsNeeded;
  }

  return Math.max(totalSegments, 1);
}
