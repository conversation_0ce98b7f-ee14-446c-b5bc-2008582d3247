import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GeminiBalanceClient, getBalanceModelNameForDatabase } from '../_shared/gemini-balance-client.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ProcessorResponse {
  success: boolean;
  processed_tasks?: number;
  errors?: string[];
  message?: string;
}

// Configuration constants
const MAX_SUMMARIES_COUNT = 80; // Maximum number of summaries to process in one task
const MAX_CONTENT_SIZE = 100000; // Maximum content size in characters (100KB)
const TRANSCRIPT_TIMEOUT_MS = 300000; // 5 minutes timeout for transcript generation
const MAX_RETRIES = 3; // Maximum number of retries for failed tasks
const HEARTBEAT_INTERVAL_MS = 30000; // 30 seconds heartbeat interval

// Initialize Gemini client with longer timeout for transcript generation
const geminiClient = new GeminiBalanceClient(undefined, TRANSCRIPT_TIMEOUT_MS);

/**
 * Clean up stuck tasks that have been in transcript_generating status for too long
 */
async function cleanupStuckTasks(supabaseClient: any): Promise<number> {
  try {
    const { data: stuckTasks } = await supabaseClient
      .from('podcast_tasks')
      .select('id, updated_at, metadata')
      .eq('status', 'transcript_generating');

    if (!stuckTasks || stuckTasks.length === 0) {
      return 0;
    }

    const tasksToCleanup = stuckTasks.filter((task: any) => {
      const updatedAt = new Date(task.updated_at);
      const now = new Date();
      const minutesSinceUpdate = (now.getTime() - updatedAt.getTime()) / (1000 * 60);
      return minutesSinceUpdate > 10; // Tasks stuck for more than 10 minutes
    });

    if (tasksToCleanup.length === 0) {
      return 0;
    }

    console.log(`🔧 Found ${tasksToCleanup.length} stuck tasks, cleaning up...`);

    for (const task of tasksToCleanup) {
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed',
          error_message: 'Task stuck in transcript_generating status for over 10 minutes - auto-cleanup',
          retry_count: (task.retry_count || 0) + 1,
          updated_at: new Date().toISOString(),
          metadata: {
            ...task.metadata,
            cleaned_up_at: new Date().toISOString(),
            cleanup_reason: 'stuck_task_auto_cleanup'
          }
        })
        .eq('id', task.id);

      console.log(`🔧 Cleaned up stuck task ${task.id}`);
    }

    return tasksToCleanup.length;
  } catch (error) {
    console.error('❌ Error during stuck task cleanup:', error);
    return 0;
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client with service role key for cron job access
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('🎙️ Starting podcast transcript processor...');

    // First, clean up any stuck tasks
    const cleanedUpCount = await cleanupStuckTasks(supabaseClient);
    if (cleanedUpCount > 0) {
      console.log(`🔧 Cleaned up ${cleanedUpCount} stuck tasks`);
    }

    // Check if any task is currently being processed (after cleanup)
    const { data: processingTasks } = await supabaseClient
      .from('podcast_tasks')
      .select('id')
      .eq('status', 'transcript_generating');

    if (processingTasks && processingTasks.length > 0) {
      console.log('⏳ Another transcript is currently being generated, skipping...');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'Another transcript generation in progress'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Find pending tasks that need transcript generation
    const { data: pendingTasks, error: fetchError } = await supabaseClient
      .from('podcast_tasks')
      .select('*')
      .eq('status', 'pending')
      .order('created_at', { ascending: true })
      .limit(1); // Process one task at a time to avoid timeout

    if (fetchError) {
      throw new Error(`Failed to fetch pending tasks: ${fetchError.message}`);
    }

    if (!pendingTasks || pendingTasks.length === 0) {
      console.log('📭 No pending tasks found');
      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: 'No pending tasks found'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const task = pendingTasks[0];
    console.log(`📝 Processing task: ${task.id} for topic: ${task.metadata?.topic_name}`);

    // Check if task has exceeded max retries
    const currentRetryCount = task.retry_count || 0;
    if (currentRetryCount >= MAX_RETRIES) {
      console.log(`❌ Task ${task.id} has exceeded max retries (${MAX_RETRIES}), marking as permanently failed`);
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed_permanent',
          error_message: `Task failed permanently after ${MAX_RETRIES} retries`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: `Task ${task.id} marked as permanently failed`
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Validate content size before processing
    const summariesData = task.metadata?.summaries_data || [];
    const contentSize = JSON.stringify(summariesData).length;

    if (summariesData.length > MAX_SUMMARIES_COUNT) {
      console.log(`❌ Task ${task.id} has too many summaries: ${summariesData.length} > ${MAX_SUMMARIES_COUNT}`);
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed_permanent',
          error_message: `Content too large: ${summariesData.length} summaries (max: ${MAX_SUMMARIES_COUNT})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: `Task ${task.id} rejected due to content size`
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (contentSize > MAX_CONTENT_SIZE) {
      console.log(`❌ Task ${task.id} content too large: ${contentSize} chars > ${MAX_CONTENT_SIZE}`);
      await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'failed_permanent',
          error_message: `Content too large: ${contentSize} characters (max: ${MAX_CONTENT_SIZE})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      return new Response(
        JSON.stringify({
          success: true,
          processed_tasks: 0,
          message: `Task ${task.id} rejected due to content size`
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`✅ Task ${task.id} validation passed: ${summariesData.length} summaries, ${contentSize} chars`);

    // Update task status to transcript_generating
    await supabaseClient
      .from('podcast_tasks')
      .update({
        status: 'transcript_generating',
        progress_percentage: 10,
        updated_at: new Date().toISOString(),
        metadata: {
          ...task.metadata,
          processing_started_at: new Date().toISOString(),
          retry_attempt: currentRetryCount + 1
        }
      })
      .eq('id', task.id);

    // Add heartbeat mechanism for long-running tasks
    const heartbeatInterval = setInterval(async () => {
      try {
        await supabaseClient
          .from('podcast_tasks')
          .update({
            updated_at: new Date().toISOString(),
            metadata: {
              ...task.metadata,
              last_heartbeat: new Date().toISOString(),
              processing_started_at: task.metadata?.processing_started_at,
              retry_attempt: task.metadata?.retry_attempt
            }
          })
          .eq('id', task.id);
        console.log(`💓 Heartbeat sent for task ${task.id}`);
      } catch (heartbeatError) {
        console.warn(`⚠️ Heartbeat failed for task ${task.id}:`, heartbeatError);
      }
    }, HEARTBEAT_INTERVAL_MS);

    try {
      // Generate transcript with enhanced timeout protection
      const transcript = await generatePodcastTranscriptWithTimeout(task);

      // Estimate segments count for TTS processing
      const estimatedSegments = estimateSegmentsCount(transcript);

      // Update task with transcript and move to next stage
      const { error: updateError } = await supabaseClient
        .from('podcast_tasks')
        .update({
          status: 'transcript_ready',
          transcript: transcript,
          total_segments: estimatedSegments,
          progress_percentage: 25,
          metadata: {
            ...task.metadata,
            transcript_generated_at: new Date().toISOString(),
            transcript_length: transcript.length,
            estimated_segments: estimatedSegments
          }
        })
        .eq('id', task.id);

      if (updateError) {
        throw new Error(`Failed to update task: ${updateError.message}`);
      }

      console.log(`✅ Successfully generated transcript for task ${task.id}`);
      console.log(`📊 Transcript length: ${transcript.length} characters, estimated segments: ${estimatedSegments}`);

    } catch (transcriptError) {
      console.error(`❌ Failed to generate transcript for task ${task.id}:`, transcriptError);

      // Determine error type for better error handling
      let errorMessage = `Transcript generation failed: ${transcriptError.message}`;
      let shouldRetry = true;
      let newRetryCount = (task.retry_count || 0) + 1;

      // Check if it's a timeout error
      if (transcriptError.message.includes('timeout') || transcriptError.message.includes('AbortError')) {
        errorMessage = `Transcript generation timed out after ${TRANSCRIPT_TIMEOUT_MS / 1000} seconds. Content may be too large.`;
        console.error(`⏰ Timeout detected for task ${task.id}. Consider splitting large content.`);
        // Timeout errors are retryable but with exponential backoff
        shouldRetry = newRetryCount < MAX_RETRIES;
      }

      // Check if it's an API error that shouldn't be retried
      if (transcriptError.message.includes('API error') && transcriptError.message.includes('400')) {
        shouldRetry = false;
        errorMessage = `API request invalid: ${transcriptError.message}`;
      }

      // Check if it's a content size error (shouldn't retry)
      if (transcriptError.message.includes('Content too large') || transcriptError.message.includes('too many summaries')) {
        shouldRetry = false;
        errorMessage = `Content size error: ${transcriptError.message}`;
      }

      // Determine final status
      let finalStatus: string;
      if (!shouldRetry || newRetryCount >= MAX_RETRIES) {
        finalStatus = 'failed_permanent';
        errorMessage = `${errorMessage} (Max retries exceeded: ${newRetryCount}/${MAX_RETRIES})`;
      } else {
        finalStatus = 'pending'; // Reset to pending for retry
        errorMessage = `${errorMessage} (Retry ${newRetryCount}/${MAX_RETRIES})`;
      }

      try {
        // Update task with error - ensure this always succeeds
        const { error: updateError } = await supabaseClient
          .from('podcast_tasks')
          .update({
            status: finalStatus,
            error_message: errorMessage,
            retry_count: newRetryCount,
            updated_at: new Date().toISOString(),
            metadata: {
              ...task.metadata,
              last_error_at: new Date().toISOString(),
              last_error_type: transcriptError.name || 'UnknownError',
              processing_started_at: task.metadata?.processing_started_at,
              retry_attempt: task.metadata?.retry_attempt
            }
          })
          .eq('id', task.id);

        if (updateError) {
          console.error(`❌ Failed to update task status after error:`, updateError);
          // Force update with minimal data to ensure status is not stuck
          await supabaseClient
            .from('podcast_tasks')
            .update({
              status: 'failed',
              error_message: 'Error updating task status',
              updated_at: new Date().toISOString()
            })
            .eq('id', task.id);
        } else {
          console.log(`📝 Task ${task.id} status updated to ${finalStatus} with error: ${errorMessage}`);
        }
      } catch (updateError) {
        console.error(`❌ Critical error: Could not update task status for ${task.id}:`, updateError);
      }

      throw transcriptError;
    } finally {
      // Always clear the heartbeat interval
      clearInterval(heartbeatInterval);
      console.log(`🛑 Heartbeat cleared for task ${task.id}`);
    }

    return new Response(
      JSON.stringify({
        success: true,
        processed_tasks: 1,
        message: `Successfully processed task ${task.id}`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('❌ Transcript processor error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        errors: [error.message]
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});

/**
 * Generate podcast transcript with enhanced timeout protection
 */
async function generatePodcastTranscriptWithTimeout(task: any): Promise<string> {
  return new Promise(async (resolve, reject) => {
    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, timeoutReject) => {
      setTimeout(() => {
        timeoutReject(new Error(`TRANSCRIPT_TIMEOUT: Generation exceeded ${TRANSCRIPT_TIMEOUT_MS / 1000} seconds`));
      }, TRANSCRIPT_TIMEOUT_MS);
    });

    // Create abort controller for manual cancellation
    const abortController = new AbortController();

    try {
      // Race between transcript generation and timeout
      const result = await Promise.race([
        generatePodcastTranscript(task, abortController.signal),
        timeoutPromise
      ]);

      resolve(result);
    } catch (error) {
      // Abort any ongoing operations
      abortController.abort();
      reject(error);
    }
  });
}

/**
 * Original transcript generation function with abort signal support
 */
async function generatePodcastTranscript(task: any, abortSignal?: AbortSignal): Promise<string> {
  const summariesData = task.metadata?.summaries_data || [];
  const taskLanguage = task.language || 'ZH'; // Default to Chinese if not specified

  if (summariesData.length === 0) {
    throw new Error('No summaries data found in task metadata');
  }

  // Check for abort signal
  if (abortSignal?.aborted) {
    throw new Error('TRANSCRIPT_ABORTED: Operation was cancelled');
  }

  // Additional content size validation
  if (summariesData.length > MAX_SUMMARIES_COUNT) {
    throw new Error(`Content too large: ${summariesData.length} summaries (max: ${MAX_SUMMARIES_COUNT})`);
  }

  // Prepare content for transcript generation based on language
  let summariesContent: string;
  let prompt: string;

  if (taskLanguage === 'EN') {
    // English content formatting
    summariesContent = summariesData.map((summary: any, index: number) => {
      return `${index + 1}. [${summary.source_name} - ${summary.platform}] ${summary.title}\n${summary.content}\n`;
    }).join('\n');

    // English prompt
    prompt = `You are a professional podcast producer. Based on the following daily summaries related to ${task.metadata?.topic_name}, generate a 40-minute English podcast script with two hosts for FeedMe.Today daily podcast.

Character Setup:
- Host (Joy): Professional, good at guiding conversations and asking questions, friendly and natural speaking style
- Guest (Sam): Technical expert, provides in-depth analysis and insights, professional but accessible language

Requirements:
1. GENERATE CONTENT ENTIRELY IN ENGLISH - this is an English podcast for English-speaking audience
2. Start with a brief mention that this is FeedMe.Today's daily podcast, then dive directly into the content discussion
3. Natural conversation flow, avoid rigid Q&A format, create authentic dialogue
4. Group related topics for discussion, deep dive into each topic thoroughly
5. Total duration MUST be 40 minutes (approximately 10000-20000 words, which is about 25000-40000 characters in English)
6. Strictly use "Joy:" and "Sam:" to mark speakers
7. Interactive dialogue with appropriate responses, supplements, and questions
8. Language should fit English podcast expression habits, natural and fluent
9. Ensure each speaking segment is moderate length for TTS processing
10. When discussing content from non-English sources, provide context and explanation in English
11. DO NOT use any Chinese characters or phrases - keep everything in English
12. Make sure all summary content are included in the podcast
13. NO opening music, closing remarks, or narrator content - only direct conversation between the two hosts
14. Focus purely on content discussion without unnecessary introductions or transitions

Today's Summary Content (${summariesData.length} items):
${summariesContent}

Please generate the complete English podcast dialogue script:`;
  } else {
    // Chinese content formatting
    summariesContent = summariesData.map((summary: any, index: number) => {
      return `${index + 1}. 【${summary.source_name} - ${summary.platform}】${summary.title}\n${summary.content}\n`;
    }).join('\n');

    // Chinese prompt
    prompt = `你是一个专业的播客制作人。请基于以下${task.metadata?.topic_name}相关的每日摘要内容，生成一个60分钟的FeedMe.Today每日播客双人对话脚本。

角色设定：
- 主持人（Alex）：专业、引导性强，负责串联话题和提问，语言风格亲切自然
- 嘉宾（Jessie）：技术专家，提供深度分析和见解，语言风格专业但易懂

要求：
1. 开头简单提及这是FeedMe.Today的每日播客，然后直接进入内容讨论
2. 自然的对话流程，避免生硬问答，要有真实的对话感
3. 按相关话题分组讨论，每个话题深入探讨
4. 总时长必须达到60分钟（约15000-30000字）
5. 严格使用"Alex："和"Jessie："标记发言人
6. 对话要有互动感，包含适当的回应、补充、提问
7. 语言要符合中文播客的表达习惯，自然流畅
8. 确保每个发言段落长度适中，便于后续TTS处理
9. 如果包含英文内容摘要，请自然地融入中文讨论中
10. 要保证摘要里面的所有内容都在播客里面说出来
11. 不要加入任何旁白、开场音乐、结尾总结等内容，只要两个主持人的纯对话
12. 专注于内容讨论，不需要不必要的介绍或过渡

今日摘要内容（共${summariesData.length}条）：
${summariesContent}

请生成完整的播客对话脚本：`;
  }

  console.log(`🤖 Generating transcript with Gemini for ${summariesData.length} summaries...`);
  console.log(`📊 Content length: ${summariesContent.length} characters`);

  try {
    // Check for abort signal before API call
    if (abortSignal?.aborted) {
      throw new Error('TRANSCRIPT_ABORTED: Operation was cancelled before API call');
    }

    const startTime = Date.now();

    // Create a promise that rejects if abort signal is triggered
    const abortPromise = new Promise<never>((_, reject) => {
      if (abortSignal) {
        abortSignal.addEventListener('abort', () => {
          reject(new Error('TRANSCRIPT_ABORTED: Operation was cancelled during API call'));
        });
      }
    });

    // Race between API call and abort signal
    const apiPromise = geminiClient.simpleChat(prompt, {
      maxTokens: 60000,
      temperature: 0.7
    });

    const content = abortSignal
      ? await Promise.race([apiPromise, abortPromise])
      : await apiPromise;

    const endTime = Date.now();

    console.log(`⏱️ Transcript generation took ${(endTime - startTime) / 1000}s`);

    if (!content || content.trim().length === 0) {
      throw new Error('No content generated from Gemini - empty response');
    }

    console.log(`📝 Generated transcript length: ${content.length} characters`);
    return content;

  } catch (error) {
    console.error('❌ Failed to generate transcript:', error);

    // Check if it was aborted
    if (error.message.includes('TRANSCRIPT_ABORTED')) {
      throw new Error(`Operation cancelled: ${error.message}`);
    }

    // Provide more specific error information
    if (error.message.includes('timeout')) {
      throw new Error(`Gemini API timeout: Content too large (${summariesContent.length} chars, ${summariesData.length} summaries)`);
    } else if (error.message.includes('AbortError')) {
      throw new Error(`Request aborted: Likely due to timeout or network issues`);
    } else if (error.message.includes('API error')) {
      throw new Error(`Gemini API error: ${error.message}`);
    } else {
      throw new Error(`Transcript generation failed: ${error.message}`);
    }
  }
}

function estimateSegmentsCount(transcript: string): number {
  // Split by speaker changes - support both Chinese and English speakers (with both colon types)
  const speakerChanges = transcript.split(/(?=(?:Alex[：:]|Jessie[：:]|Joy:|Sam:))/g).filter(segment => segment.trim().length > 0);

  // Each segment should be roughly 2 minutes of speech (about 300-400 characters)
  const targetSegmentLength = 350;
  let totalSegments = 0;

  for (const segment of speakerChanges) {
    const segmentLength = segment.length;
    const segmentsNeeded = Math.ceil(segmentLength / targetSegmentLength);
    totalSegments += segmentsNeeded;
  }

  return Math.max(totalSegments, 1);
}
